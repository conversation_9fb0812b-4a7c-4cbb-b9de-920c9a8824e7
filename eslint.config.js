/**
 * @file eslint.config.js
 *
 * @version 2.0.0
 * <AUTHOR>
 * @contributors
 * @license MIT
 *
 * @description
 * Modern ESLint configuration for The Great Calculator project.
 * Ensures maximum code quality, consistency, and best practices with strict TypeScript rules.
 * Uses ESLint v9+ flat config format for future-proof configuration.
 */

// ------------ IMPORTS
import typescriptEslint from '@typescript-eslint/eslint-plugin';
import typescriptParser from '@typescript-eslint/parser';
import jestPlugin from 'eslint-plugin-jest';
import importPlugin from 'eslint-plugin-import';
import unicornPlugin from 'eslint-plugin-unicorn';
import securityPlugin from 'eslint-plugin-security';
import sonarjsPlugin from 'eslint-plugin-sonarjs';
import promisePlugin from 'eslint-plugin-promise';
import globals from 'globals'

// ------------ TYPESCRIPT RECOMMENDED CONFIG
const tsRecommended = typescriptEslint.configs['recommended-type-checked'];

// ------------ CONFIGURATION
export default [
  // Ignore output, build, and coverage folders
  {
    ignores: [
      './eslint.config.js',
      './.vscode/**',
      './.github/**',
      './build/**',
      './dev-dist/**',
      './dist/**',
      './coverage/**',
      './public/**',
      './node_modules/**',
      './scripts/**',
      './api/**',
      '*.d.ts',
      '*.min.js',
      '*.bundle.js',
      '*.map',
      '*.html',
      '*.json',
      '*.svg',
      '*.png',
      '*.jpg',
      '*.jpeg',
      '*.gif',
      '*.ico',
      '*.pdf',
      '*.pem',
    ],
  },
  // Base configuration for all files
  {
    // Apply to TypeScript and JavaScript files
    files: ['**/*.{ts,js,mts,cts}'],

    // General configuration for all files
    linterOptions: {
      reportUnusedDisableDirectives: 'error', // Good practice
    },

    languageOptions: {
      parser: typescriptParser, // Change from eslintParserTypeScript
      parserOptions: {
        ecmaVersion: 'latest',
        sourceType: 'module',
        // ecmaVersion: 2022, // This is redundant with 'latest'
        project: './tsconfig.json',
        tsconfigRootDir: import.meta.dirname,
        warnOnUnsupportedTypeScriptVersion: false,
      },
      globals: {
        ...globals.browser,
        ...globals.node,
        ...globals.es2024,
      },
    },

    plugins: {
      '@typescript-eslint': typescriptEslint, // Change from eslintPluginTypeScript
      'import': importPlugin,
      'unicorn': unicornPlugin,
      'security': securityPlugin,
      'sonarjs': sonarjsPlugin,
      'promise': promisePlugin,
    },

    rules: {
      // ------------ ESLINT CORE RULES (RELAXED)
      'array-callback-return': 'warn',
      'block-scoped-var': 'warn',
      'complexity': ['warn', { max: 15 }],
      'consistent-return': 'warn',
      'curly': ['warn', 'multi-line'],
      'default-case': 'off',
      'default-case-last': 'off',
      'dot-notation': 'warn',
      'eqeqeq': ['warn', 'always', { null: 'ignore' }],
      'for-direction': 'error',
      'getter-return': 'error',
      'grouped-accessor-pairs': 'off',
      'guard-for-in': 'warn',
      'max-depth': ['warn', { max: 5 }],
      'max-lines': 'off',
      'max-lines-per-function': 'off',
      'max-nested-callbacks': ['warn', 5],
      'max-params': ['warn', { max: 5 }],
      'max-statements': 'off',
      'no-alert': 'warn',
      'no-array-constructor': 'warn',
      'no-caller': 'error',
      'no-console': 'off',
      'no-constructor-return': 'off',
      'no-debugger': 'warn',
      'no-duplicate-imports': 'warn',
      'no-else-return': 'off',
      'no-empty': ['warn', { allowEmptyCatch: true }],
      'no-empty-function': 'off',
      'no-eval': 'warn',
      'no-extend-native': 'warn',
      'no-extra-bind': 'warn',
      'no-extra-label': 'warn',
      'no-implicit-coercion': 'off',
      'no-implicit-globals': 'off',
      'no-implied-eval': 'warn',
      'no-invalid-this': 'off',
      'no-iterator': 'warn',
      'no-labels': 'warn',
      'no-lone-blocks': 'warn',
      'no-loop-func': 'off',
      'no-magic-numbers': 'off',
      'no-multi-assign': 'warn',
      'no-multi-str': 'warn',
      'no-nested-ternary': 'off',
      'no-new': 'warn',
      'no-new-func': 'warn',
      'no-new-wrappers': 'warn',
      'no-object-constructor': 'off',
      'no-octal-escape': 'warn',
      'no-param-reassign': 'off',
      'no-proto': 'warn',
      'no-return-assign': 'warn',
      'no-return-await': 'off',
      'no-script-url': 'warn',
      'no-self-compare': 'warn',
      'no-sequences': 'warn',
      'no-throw-literal': 'warn',
      'no-unmodified-loop-condition': 'off',
      'no-unneeded-ternary': 'warn',
      'no-unreachable-loop': 'warn',
      'no-unused-expressions': 'warn',
      'no-useless-call': 'warn',
      'no-useless-computed-key': 'warn',
      'no-useless-concat': 'warn',
      'no-useless-constructor': 'off',
      'no-useless-rename': 'warn',
      'no-useless-return': 'warn',
      'no-var': 'warn',
      'no-void': 'off',
      'no-warning-comments': ['warn', { terms: ['todo', 'fixme', 'hack'] }],
      'object-shorthand': 'warn',
      'one-var': ['warn', 'never'],
      'prefer-arrow-callback': 'warn',
      'prefer-const': 'warn',
      'prefer-destructuring': 'off',
      'prefer-exponentiation-operator': 'warn',
      'prefer-numeric-literals': 'off',
      'prefer-object-has-own': 'warn',
      'prefer-object-spread': 'warn',
      'prefer-promise-reject-errors': 'warn',
      'prefer-regex-literals': 'warn',
      'prefer-rest-params': 'warn',
      'prefer-spread': 'warn',
      'prefer-template': 'warn',
      'radix': 'warn',
      'require-atomic-updates': 'off',
      'require-await': 'off',
      'sort-imports': ['warn', { ignoreDeclarationSort: true }],
      'strict': 'off',
      'symbol-description': 'warn',
      'vars-on-top': 'off',
      'yoda': 'off',

      // ------------ TYPESCRIPT-ESLINT RULES (RELAXED)
      '@typescript-eslint/adjacent-overload-signatures': 'warn',
      '@typescript-eslint/array-type': ['warn', { default: 'array-simple' }],
      '@typescript-eslint/await-thenable': 'warn',
      '@typescript-eslint/ban-ts-comment': 'off',
      '@typescript-eslint/ban-tslint-comment': 'off',
      '@typescript-eslint/no-restricted-types': 'off',
      '@typescript-eslint/class-literal-property-style': 'off',
      '@typescript-eslint/consistent-generic-constructors': 'off',
      '@typescript-eslint/consistent-indexed-object-style': 'off',
      '@typescript-eslint/consistent-type-assertions': 'warn',
      '@typescript-eslint/consistent-type-definitions': ['warn', 'interface'],
      '@typescript-eslint/consistent-type-exports': 'off',
      '@typescript-eslint/consistent-type-imports': 'off',
      '@typescript-eslint/explicit-function-return-type': 'off',
      '@typescript-eslint/explicit-member-accessibility': 'off',
      '@typescript-eslint/explicit-module-boundary-types': 'off',
      '@typescript-eslint/member-ordering': 'off',
      '@typescript-eslint/method-signature-style': 'off',
      '@typescript-eslint/naming-convention': 'off',
      '@typescript-eslint/no-array-constructor': 'warn',
      '@typescript-eslint/no-base-to-string': 'off',
      '@typescript-eslint/no-confusing-non-null-assertion': 'off',
      '@typescript-eslint/no-confusing-void-expression': 'off',
      '@typescript-eslint/no-duplicate-enum-values': 'warn',
      '@typescript-eslint/no-duplicate-type-constituents': 'off',
      '@typescript-eslint/no-dynamic-delete': 'off',
      '@typescript-eslint/no-empty-function': 'off',
      '@typescript-eslint/no-empty-interface': 'off',
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-extra-non-null-assertion': 'off',
      '@typescript-eslint/no-extraneous-class': 'off',
      '@typescript-eslint/no-floating-promises': 'warn',
      '@typescript-eslint/no-for-in-array': 'off',
      '@typescript-eslint/no-implied-eval': 'warn',
      '@typescript-eslint/no-import-type-side-effects': 'off',
      '@typescript-eslint/no-inferrable-types': 'off',
      '@typescript-eslint/no-invalid-void-type': 'off',
      '@typescript-eslint/no-loop-func': 'off',
      '@typescript-eslint/no-meaningless-void-operator': 'off',
      '@typescript-eslint/no-misused-new': 'warn',
      '@typescript-eslint/no-misused-promises': 'warn',
      '@typescript-eslint/no-mixed-enums': 'off',
      '@typescript-eslint/no-namespace': 'off',
      '@typescript-eslint/no-non-null-asserted-nullish-coalescing': 'off',
      '@typescript-eslint/no-non-null-asserted-optional-chain': 'off',
      '@typescript-eslint/no-non-null-assertion': 'off',
      '@typescript-eslint/no-redundant-type-constituents': 'off',
      '@typescript-eslint/no-require-imports': 'off',
      '@typescript-eslint/no-restricted-imports': 'off',
      '@typescript-eslint/no-this-alias': 'off',
      '@typescript-eslint/only-throw-error': 'off',
      '@typescript-eslint/no-unnecessary-boolean-literal-compare': 'off',
      '@typescript-eslint/no-unnecessary-condition': 'off',
      '@typescript-eslint/no-unnecessary-qualifier': 'off',
      '@typescript-eslint/no-unnecessary-type-arguments': 'off',
      '@typescript-eslint/no-unnecessary-type-assertion': 'off',
      '@typescript-eslint/no-unnecessary-type-constraint': 'off',
      '@typescript-eslint/no-unsafe-argument': 'warn',
      '@typescript-eslint/no-unsafe-assignment': 'warn',
      '@typescript-eslint/no-unsafe-call': 'warn',
      '@typescript-eslint/no-unsafe-declaration-merging': 'off',
      '@typescript-eslint/no-unsafe-enum-comparison': 'off',
      '@typescript-eslint/no-unsafe-member-access': 'warn',
      '@typescript-eslint/no-unsafe-return': 'warn',
      '@typescript-eslint/no-unsafe-unary-minus': 'off',
      '@typescript-eslint/no-unused-vars': ['warn', {
        argsIgnorePattern: '^_',
        varsIgnorePattern: '^_',
        caughtErrorsIgnorePattern: '^_',
      }],
      '@typescript-eslint/no-use-before-define': 'off',
      '@typescript-eslint/no-useless-constructor': 'off',
      '@typescript-eslint/no-useless-empty-export': 'off',
      '@typescript-eslint/no-var-requires': 'off',
      '@typescript-eslint/non-nullable-type-assertion-style': 'off',
      '@typescript-eslint/prefer-as-const': 'off',
      '@typescript-eslint/prefer-destructuring': 'off',
      '@typescript-eslint/prefer-enum-initializers': 'off',
      '@typescript-eslint/prefer-for-of': 'off',
      '@typescript-eslint/prefer-function-type': 'off',
      '@typescript-eslint/prefer-includes': 'off',
      '@typescript-eslint/prefer-literal-enum-member': 'off',
      '@typescript-eslint/prefer-namespace-keyword': 'off',
      '@typescript-eslint/prefer-nullish-coalescing': 'off',
      '@typescript-eslint/prefer-optional-chain': 'off',
      '@typescript-eslint/prefer-readonly': 'off',
      '@typescript-eslint/prefer-reduce-type-parameter': 'off',
      '@typescript-eslint/prefer-regexp-exec': 'off',
      '@typescript-eslint/prefer-return-this-type': 'off',
      '@typescript-eslint/prefer-string-starts-ends-with': 'off',
      '@typescript-eslint/prefer-ts-expect-error': 'off',
      '@typescript-eslint/promise-function-async': 'off',
      '@typescript-eslint/require-array-sort-compare': 'off',
      '@typescript-eslint/restrict-plus-operands': 'off',
      '@typescript-eslint/restrict-template-expressions': 'off',
      '@typescript-eslint/return-await': 'off',
      '@typescript-eslint/strict-boolean-expressions': 'off',
      '@typescript-eslint/switch-exhaustiveness-check': 'off',
      '@typescript-eslint/triple-slash-reference': 'off',
      '@typescript-eslint/unbound-method': 'off',
      '@typescript-eslint/unified-signatures': 'off',

      // ------------ IMPORT PLUGIN RULES (RELAXED)
      'import/no-unresolved': 'warn',
      'import/named': 'warn',
      'import/default': 'warn',
      'import/namespace': 'warn',
      'import/no-absolute-path': 'warn',
      'import/no-dynamic-require': 'off',
      'import/no-internal-modules': 'off',
      'import/no-webpack-loader-syntax': 'warn',
      'import/no-self-import': 'warn',
      'import/no-cycle': 'warn',
      'import/no-useless-path-segments': 'warn',
      'import/no-relative-parent-imports': 'off',
      'import/no-relative-packages': 'off',
      'import/export': 'warn',
      'import/no-named-as-default': 'warn',
      'import/no-named-as-default-member': 'warn',
      'import/no-deprecated': 'warn',
      'import/no-extraneous-dependencies': 'warn',
      'import/no-mutable-exports': 'warn',
      'import/no-unused-modules': 'off',
      'import/unambiguous': 'off',
      'import/no-commonjs': 'off',
      'import/no-amd': 'off',
      'import/no-nodejs-modules': 'off',
      'import/first': 'warn',
      'import/exports-last': 'off',
      'import/no-duplicates': 'warn',
      'import/no-namespace': 'off',
      'import/extensions': ['warn', 'ignorePackages'],
      'import/order': ['warn', {
        'groups': [
          'builtin',
          'external',
          'internal',
          'parent',
          'sibling',
          'index',
          'object',
          'type',
        ],
        'newlines-between': 'always',
        'alphabetize': {
          'order': 'asc',
          'caseInsensitive': true,
        },
      }],
      'import/newline-after-import': 'warn',
      'import/prefer-default-export': 'off',
      'import/max-dependencies': 'off',
      'import/no-unassigned-import': 'off',
      'import/no-named-default': 'off',
      'import/no-default-export': 'off',
      'import/no-named-export': 'off',
      'import/no-anonymous-default-export': 'off',
      'import/group-exports': 'off',
      'import/dynamic-import-chunkname': 'off',

      // ------------ UNICORN PLUGIN RULES (RELAXED)
      'unicorn/better-regex': 'warn',
      'unicorn/catch-error-name': 'off',
      'unicorn/consistent-destructuring': 'off',
      'unicorn/consistent-function-scoping': 'off',
      'unicorn/custom-error-definition': 'off',
      'unicorn/empty-brace-spaces': 'off',
      'unicorn/error-message': 'warn',
      'unicorn/escape-case': 'off',
      'unicorn/expiring-todo-comments': 'off',
      'unicorn/explicit-length-check': 'off',
      'unicorn/filename-case': 'off',
      'unicorn/new-for-builtins': 'off',
      'unicorn/no-abusive-eslint-disable': 'warn',
      'unicorn/no-array-callback-reference': 'off',
      'unicorn/no-array-for-each': 'off',
      'unicorn/no-array-method-this-argument': 'off',
      'unicorn/no-array-push-push': 'off',
      'unicorn/no-array-reduce': 'off',
      'unicorn/no-await-expression-member': 'off',
      'unicorn/no-console-spaces': 'off',
      'unicorn/no-document-cookie': 'off',
      'unicorn/no-empty-file': 'off',
      'unicorn/no-for-loop': 'off',
      'unicorn/no-hex-escape': 'off',
      'unicorn/no-instanceof-array': 'off',
      'unicorn/no-invalid-remove-event-listener': 'off',
      'unicorn/no-lonely-if': 'off',
      'unicorn/no-negated-condition': 'off',
      'unicorn/no-nested-ternary': 'off',
      'unicorn/no-new-array': 'off',
      'unicorn/no-new-buffer': 'off',
      'unicorn/no-null': 'off',
      'unicorn/no-object-as-default-parameter': 'off',
      'unicorn/no-process-exit': 'off',
      'unicorn/no-static-only-class': 'off',
      'unicorn/no-thenable': 'off',
      'unicorn/no-this-assignment': 'off',
      'unicorn/no-unnecessary-await': 'off',
      'unicorn/no-unreadable-array-destructuring': 'off',
      'unicorn/no-unreadable-iife': 'off',
      'unicorn/no-unused-properties': 'off',
      'unicorn/no-useless-fallback-in-spread': 'off',
      'unicorn/no-useless-length-check': 'off',
      'unicorn/no-useless-promise-resolve-reject': 'off',
      'unicorn/no-useless-spread': 'off',
      'unicorn/no-useless-switch-case': 'off',
      'unicorn/no-zero-fractions': 'off',
      'unicorn/number-literal-case': 'off',
      'unicorn/numeric-separators-style': 'off',
      'unicorn/prefer-add-event-listener': 'off',
      'unicorn/prefer-array-find': 'off',
      'unicorn/prefer-array-flat': 'off',
      'unicorn/prefer-array-flat-map': 'off',
      'unicorn/prefer-array-index-of': 'off',
      'unicorn/prefer-array-some': 'off',
      'unicorn/prefer-at': 'off',
      'unicorn/prefer-code-point': 'off',
      'unicorn/prefer-date-now': 'off',
      'unicorn/prefer-default-parameters': 'off',
      'unicorn/prefer-dom-node-append': 'off',
      'unicorn/prefer-dom-node-dataset': 'off',
      'unicorn/prefer-dom-node-remove': 'off',
      'unicorn/prefer-dom-node-text-content': 'off',
      'unicorn/prefer-includes': 'off',
      'unicorn/prefer-keyboard-event-key': 'off',
      'unicorn/prefer-math-trunc': 'off',
      'unicorn/prefer-modern-dom-apis': 'off',
      'unicorn/prefer-modern-math-apis': 'off',
      'unicorn/prefer-module': 'off',
      'unicorn/prefer-native-coercion-functions': 'off',
      'unicorn/prefer-negative-index': 'off',
      'unicorn/prefer-node-protocol': 'off',
      'unicorn/prefer-number-properties': 'off',
      'unicorn/prefer-object-from-entries': 'off',
      'unicorn/prefer-optional-catch-binding': 'off',
      'unicorn/prefer-prototype-methods': 'off',
      'unicorn/prefer-query-selector': 'off',
      'unicorn/prefer-reflect-apply': 'off',
      'unicorn/prefer-regexp-test': 'off',
      'unicorn/prefer-set-has': 'off',
      'unicorn/prefer-set-size': 'off',
      'unicorn/prefer-spread': 'off',
      'unicorn/prefer-string-replace-all': 'off',
      'unicorn/prefer-string-slice': 'off',
      'unicorn/prefer-string-starts-ends-with': 'off',
      'unicorn/prefer-string-trim-start-end': 'off',
      'unicorn/prefer-switch': 'off',
      'unicorn/prefer-ternary': 'off',
      'unicorn/prefer-top-level-await': 'off',
      'unicorn/prefer-type-error': 'off',
      'unicorn/prevent-abbreviations': 'off',
      'unicorn/relative-url-style': 'off',
      'unicorn/require-array-join-separator': 'off',
      'unicorn/require-number-to-fixed-digits-argument': 'off',
      'unicorn/require-post-message-target-origin': 'off',
      'unicorn/string-content': 'off',
      'unicorn/switch-case-braces': 'off',
      'unicorn/template-indent': 'off',
      'unicorn/text-encoding-identifier-case': 'off',
      'unicorn/throw-new-error': 'off',

      // ------------ SECURITY PLUGIN RULES (RELAXED)
      'security/detect-buffer-noassert': 'off',
      'security/detect-child-process': 'off',
      'security/detect-disable-mustache-escape': 'off',
      'security/detect-eval-with-expression': 'warn',
      'security/detect-new-buffer': 'off',
      'security/detect-no-csrf-before-method-override': 'off',
      'security/detect-non-literal-fs-filename': 'off',
      'security/detect-non-literal-regexp': 'off',
      'security/detect-non-literal-require': 'off',
      'security/detect-object-injection': 'off',
      'security/detect-possible-timing-attacks': 'off',
      'security/detect-pseudoRandomBytes': 'off',
      'security/detect-unsafe-regex': 'off',

      // ------------ SONARJS PLUGIN RULES (RELAXED)
      'sonarjs/cognitive-complexity': ['warn', 20],
      'sonarjs/elseif-without-else': 'off',
      'sonarjs/max-switch-cases': ['warn', 40],
      'sonarjs/no-all-duplicated-branches': 'off',
      'sonarjs/no-collapsible-if': 'off',
      'sonarjs/no-collection-size-mischeck': 'off',
      'sonarjs/no-duplicate-string': 'off',
      'sonarjs/no-duplicated-branches': 'off',
      'sonarjs/no-element-overwrite': 'off',
      'sonarjs/no-empty-collection': 'off',
      'sonarjs/no-extra-arguments': 'off',
      'sonarjs/no-gratuitous-expressions': 'off',
      'sonarjs/no-identical-conditions': 'off',
      'sonarjs/no-identical-expressions': 'off',
      'sonarjs/no-ignored-return': 'off',
      'sonarjs/no-inverted-boolean-check': 'off',
      'sonarjs/no-nested-switch': 'off',
      'sonarjs/no-nested-template-literals': 'off',
      'sonarjs/no-one-iteration-loop': 'off',
      'sonarjs/no-redundant-boolean': 'off',
      'sonarjs/no-redundant-jump': 'off',
      'sonarjs/no-same-line-conditional': 'off',
      'sonarjs/no-small-switch': 'off',
      'sonarjs/no-unused-collection': 'off',
      'sonarjs/no-use-of-empty-return-value': 'off',
      'sonarjs/no-useless-catch': 'off',
      'sonarjs/non-existent-operator': 'off',
      'sonarjs/prefer-immediate-return': 'off',
      'sonarjs/prefer-object-literal': 'off',
      'sonarjs/prefer-single-boolean-return': 'off',
      'sonarjs/prefer-while': 'off',

      // ------------ PROMISE PLUGIN RULES (RELAXED)
      'promise/always-return': 'off',
      'promise/avoid-new': 'off',
      'promise/catch-or-return': 'warn',
      'promise/no-callback-in-promise': 'off',
      'promise/no-native': 'off',
      'promise/no-nesting': 'off',
      'promise/no-new-statics': 'off',
      'promise/no-promise-in-callback': 'off',
      'promise/no-return-in-finally': 'off',
      'promise/no-return-wrap': 'off',
      'promise/param-names': 'off',
      'promise/prefer-await-to-callbacks': 'off',
      'promise/prefer-await-to-then': 'off',
      'promise/valid-params': 'off',
    },
  },

  // ------------ CONFIGURATION FILES OVERRIDES
  {
    files: [
      '*.config.{js,ts,mjs,cjs}',
      '.eslintrc.{js,cjs}',
      'vite.config.*',
      'vitest.config.*',
      'webpack.config.*',
      'rollup.config.*',
      'babel.config.*',
      'prettier.config.*',
      'tailwind.config.*',
      'postcss.config.*',
      './jest.config.js',
      './playwright.config.js',
      './eslint.config.js',
      './prettier.config.js',
      './vite.config.js',
      './webpack.config.js',
    ],
    languageOptions: {
      parserOptions: {
        project: undefined, // Disable type-aware linting for config files
      },
    },
    rules: {
      'import/no-default-export': 'off',
      '@typescript-eslint/no-unsafe-assignment': 'off',
      '@typescript-eslint/no-unsafe-member-access': 'off',
      'no-console': 'off',
      'unicorn/prefer-module': 'off',
      'unicorn/prevent-abbreviations': 'off',
    },
  },

  // ------------ JAVASCRIPT LEGACY FILES OVERRIDES
  {
    files: ['**/*.js', '*.js'],
    languageOptions: {
      parserOptions: {
        project: undefined, // Disable type-aware linting for all JS files, including root
      },
    },
    rules: {
      '@typescript-eslint/explicit-function-return-type': 'off',
      '@typescript-eslint/explicit-module-boundary-types': 'off',
      '@typescript-eslint/no-unsafe-assignment': 'off',
      '@typescript-eslint/no-unsafe-member-access': 'off',
      '@typescript-eslint/no-unsafe-call': 'off',
      '@typescript-eslint/no-unsafe-return': 'off',
      '@typescript-eslint/no-unsafe-argument': 'off',
      '@typescript-eslint/restrict-template-expressions': 'off',
      '@typescript-eslint/restrict-plus-operands': 'off',
    },
  },

  // ------------ TEST FILE OVERRIDES
  {
    files: ['**/*.{test,spec}.{ts,js}', '**/__tests__/**/*', 'tests/**/*.js'],
    languageOptions: {
      parserOptions: {
        project: undefined, // Disable type-aware linting for test files
      },
      globals: {
        ...globals.jest,
      },
    },
    plugins: {
      'jest': jestPlugin,
    },
    rules: {
      // Relax some rules for test files
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-non-null-assertion': 'off',
      '@typescript-eslint/no-unsafe-assignment': 'off',
      '@typescript-eslint/no-unsafe-member-access': 'off',
      '@typescript-eslint/no-unsafe-call': 'off',
      '@typescript-eslint/no-unsafe-return': 'off',
      '@typescript-eslint/no-unsafe-argument': 'off',
      '@typescript-eslint/unbound-method': 'off',
      'max-lines': 'off',
      'max-lines-per-function': 'off',
      'max-statements': 'off',
      'no-magic-numbers': 'off',
      'no-undefined': 'off',
      'unicorn/no-null': 'off',
      'unicorn/prevent-abbreviations': 'off',
      'sonarjs/no-duplicate-string': 'off',

      // Jest-specific rules
      'jest/consistent-test-it': ['error', { fn: 'test' }],
      'jest/expect-expect': 'error',
      'jest/max-expects': ['error', { max: 5 }],
      'jest/max-nested-describe': ['error', { max: 3 }],
      'jest/no-alias-methods': 'error',
      'jest/no-commented-out-tests': 'error',
      'jest/no-conditional-expect': 'error',
      'jest/no-conditional-in-test': 'error',
      'jest/no-deprecated-functions': 'error',
      'jest/no-disabled-tests': 'error',
      'jest/no-done-callback': 'error',
      'jest/no-duplicate-hooks': 'error',
      'jest/no-export': 'error',
      'jest/no-focused-tests': 'error',
      'jest/no-hooks': 'off',
      'jest/no-identical-title': 'error',
      'jest/no-interpolation-in-snapshots': 'error',
      'jest/no-jasmine-globals': 'error',
      'jest/no-large-snapshots': ['error', { maxSize: 50 }],
      'jest/no-mocks-import': 'error',
      'jest/no-restricted-matchers': 'off',
      'jest/no-standalone-expect': 'error',
      'jest/no-test-prefixes': 'error',
      'jest/no-test-return-statement': 'error',
      'jest/prefer-called-with': 'error',
      'jest/prefer-comparison-matcher': 'error',
      'jest/prefer-each': 'error',
      'jest/prefer-equality-matcher': 'error',
      'jest/prefer-expect-assertions': 'off',
      'jest/prefer-expect-resolves': 'error',
      'jest/prefer-hooks-in-order': 'error',
      'jest/prefer-hooks-on-top': 'error',
      'jest/prefer-lowercase-title': 'error',
      'jest/prefer-mock-promise-shorthand': 'error',
      'jest/prefer-spy-on': 'error',
      'jest/prefer-strict-equal': 'error',
      'jest/prefer-to-be': 'error',
      'jest/prefer-to-contain': 'error',
      'jest/prefer-to-have-length': 'error',
      'jest/prefer-todo': 'error',
      'jest/require-hook': 'error',
      'jest/require-to-throw-message': 'error',
      'jest/require-top-level-describe': 'error',
      'jest/valid-describe-callback': 'error',
      'jest/valid-expect': 'error',
      'jest/valid-expect-in-promise': 'error',
      'jest/valid-title': 'error',
    },
  },

  // ------------ DECLARATION FILES OVERRIDES
  {
    files: ['**/*.d.ts'],

    rules: {
      // Relax some rules for declaration files
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-restricted-types': 'off',
      'import/no-default-export': 'off',
      'unicorn/filename-case': 'off',
      'init-declarations': 'off',
      'no-var': 'off',
    },
  },
];
